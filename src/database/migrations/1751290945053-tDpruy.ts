import typeorm = require('typeorm');

class TDpruy1751290945053 implements typeorm.MigrationInterface {
    name = 'TDpruy1751290945053'

    public async up(queryRunner: typeorm.QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`rbac_role\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL COMMENT '角色名称', \`label\` varchar(255) NULL COMMENT '显示名称', \`description\` text NULL COMMENT '角色描述', \`systemed\` tinyint NOT NULL COMMENT '是否为不可更改的系统权限' DEFAULT 0, \`deletedAt\` datetime(6) NULL COMMENT '删除时间', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`rbac_permission\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL COMMENT '权限名称', \`label\` varchar(255) NULL COMMENT '权限显示名', \`description\` text NULL COMMENT '权限描述', \`rule\` text NOT NULL COMMENT '权限规则', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`rbac_role_users_user\` (\`rbacRoleId\` varchar(36) NOT NULL, \`userId\` varchar(36) NOT NULL, INDEX \`IDX_5b90c7d494d1ef3ddb732dd87e\` (\`rbacRoleId\`), INDEX \`IDX_244a13fac2fbdeb2e39cde6c6e\` (\`userId\`), PRIMARY KEY (\`rbacRoleId\`, \`userId\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`rbac_permission_roles_rbac_role\` (\`rbacPermissionId\` varchar(36) NOT NULL, \`rbacRoleId\` varchar(36) NOT NULL, INDEX \`IDX_80fbde81a8062f9b5ee2db0d5d\` (\`rbacPermissionId\`), INDEX \`IDX_30867512676b3da0981e90d299\` (\`rbacRoleId\`), PRIMARY KEY (\`rbacPermissionId\`, \`rbacRoleId\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`rbac_permission_users_user\` (\`rbacPermissionId\` varchar(36) NOT NULL, \`userId\` varchar(36) NOT NULL, INDEX \`IDX_98998ef0e9ecdd5319b9163e2a\` (\`rbacPermissionId\`), INDEX \`IDX_8d698b7a2d1504dfe609e8583b\` (\`userId\`), PRIMARY KEY (\`rbacPermissionId\`, \`userId\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`rbac_role_users_user\` ADD CONSTRAINT \`FK_5b90c7d494d1ef3ddb732dd87ea\` FOREIGN KEY (\`rbacRoleId\`) REFERENCES \`rbac_role\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`rbac_role_users_user\` ADD CONSTRAINT \`FK_244a13fac2fbdeb2e39cde6c6e6\` FOREIGN KEY (\`userId\`) REFERENCES \`user\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`rbac_permission_roles_rbac_role\` ADD CONSTRAINT \`FK_80fbde81a8062f9b5ee2db0d5de\` FOREIGN KEY (\`rbacPermissionId\`) REFERENCES \`rbac_permission\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`rbac_permission_roles_rbac_role\` ADD CONSTRAINT \`FK_30867512676b3da0981e90d299a\` FOREIGN KEY (\`rbacRoleId\`) REFERENCES \`rbac_role\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`rbac_permission_users_user\` ADD CONSTRAINT \`FK_98998ef0e9ecdd5319b9163e2a9\` FOREIGN KEY (\`rbacPermissionId\`) REFERENCES \`rbac_permission\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`rbac_permission_users_user\` ADD CONSTRAINT \`FK_8d698b7a2d1504dfe609e8583b0\` FOREIGN KEY (\`userId\`) REFERENCES \`user\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: typeorm.QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`rbac_permission_users_user\` DROP FOREIGN KEY \`FK_8d698b7a2d1504dfe609e8583b0\``);
        await queryRunner.query(`ALTER TABLE \`rbac_permission_users_user\` DROP FOREIGN KEY \`FK_98998ef0e9ecdd5319b9163e2a9\``);
        await queryRunner.query(`ALTER TABLE \`rbac_permission_roles_rbac_role\` DROP FOREIGN KEY \`FK_30867512676b3da0981e90d299a\``);
        await queryRunner.query(`ALTER TABLE \`rbac_permission_roles_rbac_role\` DROP FOREIGN KEY \`FK_80fbde81a8062f9b5ee2db0d5de\``);
        await queryRunner.query(`ALTER TABLE \`rbac_role_users_user\` DROP FOREIGN KEY \`FK_244a13fac2fbdeb2e39cde6c6e6\``);
        await queryRunner.query(`ALTER TABLE \`rbac_role_users_user\` DROP FOREIGN KEY \`FK_5b90c7d494d1ef3ddb732dd87ea\``);
        await queryRunner.query(`DROP INDEX \`IDX_8d698b7a2d1504dfe609e8583b\` ON \`rbac_permission_users_user\``);
        await queryRunner.query(`DROP INDEX \`IDX_98998ef0e9ecdd5319b9163e2a\` ON \`rbac_permission_users_user\``);
        await queryRunner.query(`DROP TABLE \`rbac_permission_users_user\``);
        await queryRunner.query(`DROP INDEX \`IDX_30867512676b3da0981e90d299\` ON \`rbac_permission_roles_rbac_role\``);
        await queryRunner.query(`DROP INDEX \`IDX_80fbde81a8062f9b5ee2db0d5d\` ON \`rbac_permission_roles_rbac_role\``);
        await queryRunner.query(`DROP TABLE \`rbac_permission_roles_rbac_role\``);
        await queryRunner.query(`DROP INDEX \`IDX_244a13fac2fbdeb2e39cde6c6e\` ON \`rbac_role_users_user\``);
        await queryRunner.query(`DROP INDEX \`IDX_5b90c7d494d1ef3ddb732dd87e\` ON \`rbac_role_users_user\``);
        await queryRunner.query(`DROP TABLE \`rbac_role_users_user\``);
        await queryRunner.query(`DROP TABLE \`rbac_permission\``);
        await queryRunner.query(`DROP TABLE \`rbac_role\``);
    }

}

module.exports = TDpruy1751290945053
